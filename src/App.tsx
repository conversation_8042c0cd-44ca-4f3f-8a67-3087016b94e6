import { useState } from 'react'
import reactLogo from './assets/react.svg'
import viteLogo from '/vite.svg'
import './App.css'
import type { IwencaiRequestData } from './types/electron'

function App() {
  const [count, setCount] = useState(0)
  const [cookies, setCookies] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')

  // API相关状态
  const [apiLoading, setApiLoading] = useState(false)
  const [apiError, setApiError] = useState<string>('')
  const [apiResult, setApiResult] = useState<any>(null)
  const [question, setQuestion] = useState<string>('6月份个股涨幅排名创业板')

  // 快速获取cookie相关状态
  const [fastLoading, setFastLoading] = useState(false)
  const [fastError, setFastError] = useState<string>('')

  // 获取问财网站cookies（原方法）
  const handleGetCookies = async () => {
    setLoading(true)
    setError('')
    setFastError('')
    setCookies('')

    try {
      const iwencaiUrl = 'https://www.iwencai.com/unifiedwap/result?w=%E6%B6%A8%E5%B9%85%E6%A6%9C&querytype=stock'
      const result = await window.electronAPI.getIwencaiCookies(iwencaiUrl)

      if (result.success) {
        setCookies(result.cookies || '')
        console.log('获取到的cookies:', result.cookies)
      } else {
        setError(result.error || '获取cookies失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setLoading(false)
    }
  }

  // 快速获取问财网站cookies
  const handleGetCookiesFast = async () => {
    setFastLoading(true)
    setFastError('')
    setError('')
    setCookies('')

    try {
      const result = await window.electronAPI.getIwencaiCookiesFast()

      if (result.success) {
        setCookies(result.cookies || '')
        console.log('快速获取到的cookies:', result.cookies)
      } else {
        setFastError(result.error || '快速获取cookies失败')
      }
    } catch (err) {
      setFastError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setFastLoading(false)
    }
  }

  // 调用问财API
  const handleCallAPI = async () => {
    if (!cookies) {
      setApiError('请先获取cookies')
      return
    }

    setApiLoading(true)
    setApiError('')
    setApiResult(null)

    try {
      // 构建请求数据，基于curl请求
      const requestData: IwencaiRequestData = {
        source: "Ths_iwencai_Xuangu",
        version: "2.0",
        query_area: "",
        block_list: "",
        add_info: "{\"urp\":{\"scene\":1,\"company\":1,\"business\":1},\"contentType\":\"json\",\"searchInfo\":true}",
        question: question,
        perpage: "50",
        page: 1,
        secondary_intent: "stock",
        log_info: "{\"input_type\":\"click\"}",
        rsh: "Ths_iwencai_Xuangu_ly8c74fp3ynszbvjf2u2wrl6x6h11l3o"
      }

      const result = await window.electronAPI.callIwencaiAPI(cookies, requestData)

      if (result.success) {
        setApiResult(result.data)
        console.log('API调用成功:', result.data)
      } else {
        setApiError(result.error || 'API调用失败')
      }
    } catch (err) {
      setApiError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setApiLoading(false)
    }
  }

  return (
    <>
      <div>
        <a href="https://vite.dev" target="_blank">
          <img src={viteLogo} className="logo" alt="Vite logo" />
        </a>
        <a href="https://react.dev" target="_blank">
          <img src={reactLogo} className="logo react" alt="React logo" />
        </a>
      </div>
      <h1>Vite + React + Electron</h1>
      <div className="card">
        <button onClick={() => setCount((count) => count + 1)}>
          count is {count}
        </button>
        <p>
          Edit <code>src/App.tsx</code> and save to test HMR
        </p>
      </div>

      <div className="card">
        <h2>问财网站Cookie获取</h2>
        <div style={{ marginBottom: '10px' }}>
          <button
            onClick={handleGetCookies}
            disabled={loading || fastLoading}
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: (loading || fastLoading) ? '#ccc' : '#646cff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: (loading || fastLoading) ? 'not-allowed' : 'pointer',
              marginRight: '10px'
            }}
          >
            {loading ? '获取中...' : '获取问财Cookie（完整方法）'}
          </button>

          <button
            onClick={handleGetCookiesFast}
            disabled={loading || fastLoading}
            style={{
              padding: '10px 20px',
              fontSize: '16px',
              backgroundColor: (loading || fastLoading) ? '#ccc' : '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: (loading || fastLoading) ? 'not-allowed' : 'pointer'
            }}
          >
            {fastLoading ? '快速获取中...' : '快速获取Cookie'}
          </button>
        </div>

        <div style={{ fontSize: '14px', color: '#666', marginBottom: '10px' }}>
          <p style={{ margin: '5px 0' }}>
            <strong>完整方法</strong>：加载完整页面获取cookies，更稳定但较慢（~30秒）
          </p>
          <p style={{ margin: '5px 0' }}>
            <strong>快速方法</strong>：直接HTTP请求获取cookies，速度快但可能不完整（~5秒）
          </p>
        </div>

        {error && (
          <div style={{
            color: 'red',
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#ffe6e6',
            borderRadius: '4px'
          }}>
            完整方法错误: {error}
          </div>
        )}

        {fastError && (
          <div style={{
            color: 'red',
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#ffe6e6',
            borderRadius: '4px'
          }}>
            快速方法错误: {fastError}
          </div>
        )}

        {cookies && (
          <div style={{ marginTop: '10px' }}>
            <h3>获取到的Cookies:</h3>
            <textarea
              value={cookies}
              readOnly
              style={{
                width: '100%',
                height: '100px',
                padding: '10px',
                fontSize: '12px',
                fontFamily: 'monospace',
                border: '1px solid #ccc',
                borderRadius: '4px',
                backgroundColor: '#f5f5f5'
              }}
            />
            <button
              onClick={() => navigator.clipboard.writeText(cookies)}
              style={{
                marginTop: '5px',
                padding: '5px 10px',
                fontSize: '14px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              复制到剪贴板
            </button>
          </div>
        )}
      </div>

      <div className="card">
        <h2>问财API调用</h2>
        <div style={{ marginBottom: '10px' }}>
          <label style={{ display: 'block', marginBottom: '5px' }}>
            查询问题:
          </label>
          <input
            type="text"
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            style={{
              width: '100%',
              padding: '8px',
              fontSize: '14px',
              border: '1px solid #ccc',
              borderRadius: '4px'
            }}
            placeholder="输入查询问题，如：6月份个股涨幅排名创业板"
          />
        </div>

        <button
          onClick={handleCallAPI}
          disabled={apiLoading || !cookies}
          style={{
            padding: '10px 20px',
            fontSize: '16px',
            backgroundColor: (apiLoading || !cookies) ? '#ccc' : '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: (apiLoading || !cookies) ? 'not-allowed' : 'pointer',
            marginRight: '10px'
          }}
        >
          {apiLoading ? '调用中...' : '调用问财API'}
        </button>

        {!cookies && (
          <span style={{ color: '#ff6b6b', fontSize: '14px' }}>
            请先获取cookies
          </span>
        )}

        {apiError && (
          <div style={{
            color: 'red',
            marginTop: '10px',
            padding: '10px',
            backgroundColor: '#ffe6e6',
            borderRadius: '4px'
          }}>
            错误: {apiError}
          </div>
        )}

        {apiResult && (
          <div style={{ marginTop: '10px' }}>
            <h3>API响应结果:</h3>
            <textarea
              value={JSON.stringify(apiResult, null, 2)}
              readOnly
              style={{
                width: '100%',
                height: '300px',
                padding: '10px',
                fontSize: '12px',
                fontFamily: 'monospace',
                border: '1px solid #ccc',
                borderRadius: '4px',
                backgroundColor: '#f5f5f5'
              }}
            />
            <button
              onClick={() => navigator.clipboard.writeText(JSON.stringify(apiResult, null, 2))}
              style={{
                marginTop: '5px',
                padding: '5px 10px',
                fontSize: '14px',
                backgroundColor: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              复制结果到剪贴板
            </button>
          </div>
        )}
      </div>

      <div className="electron-info">
        <p>
          We are using Node.js <span id="node-version"></span>,
          Chromium <span id="chrome-version"></span>,
          and Electron <span id="electron-version"></span>.
        </p>
      </div>
      <p className="read-the-docs">
        Click on the Vite and React logos to learn more
      </p>
    </>
  )
}

export default App
