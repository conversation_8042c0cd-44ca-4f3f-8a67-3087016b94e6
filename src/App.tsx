import { useState, useRef, useCallback } from 'react'
import './App.css'
import type { IwencaiRequestData } from './types/electron'

function App() {
  // 界面状态
  const [activeView, setActiveView] = useState<string>('home') // home, watchlist, bidding
  const [watchlistVisible, setWatchlistVisible] = useState(true)
  const [leftPanelWidth, setLeftPanelWidth] = useState(300)
  const [rightPanelWidth, setRightPanelWidth] = useState(400)

  // 拖拽相关
  const [isDraggingLeft, setIsDraggingLeft] = useState(false)
  const [isDraggingRight, setIsDraggingRight] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 原有功能状态
  const [cookies, setCookies] = useState<string>('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const [apiLoading, setApiLoading] = useState(false)
  const [apiError, setApiError] = useState<string>('')
  const [apiResult, setApiResult] = useState<any>(null)
  const [question, setQuestion] = useState<string>('6月份个股涨幅排名创业板')
  const [fastLoading, setFastLoading] = useState(false)
  const [fastError, setFastError] = useState<string>('')
  const [fastMethod, setFastMethod] = useState<string>('auto')
  const [lastMethod, setLastMethod] = useState<string>('')
  const [lastDuration, setLastDuration] = useState<number>(0)

  // 搜索相关
  const [searchQuery, setSearchQuery] = useState<string>('')

  // 拖拽处理函数
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()

    if (isDraggingLeft) {
      const newWidth = e.clientX - containerRect.left - 48 // 减去活动栏宽度
      if (newWidth >= 200 && newWidth <= 500) {
        setLeftPanelWidth(newWidth)
      }
    }

    if (isDraggingRight) {
      const newWidth = containerRect.right - e.clientX
      if (newWidth >= 300 && newWidth <= 800) {
        setRightPanelWidth(newWidth)
      }
    }
  }, [isDraggingLeft, isDraggingRight])

  const handleMouseUp = useCallback(() => {
    setIsDraggingLeft(false)
    setIsDraggingRight(false)
  }, [])

  // 添加全局鼠标事件监听
  useState(() => {
    if (isDraggingLeft || isDraggingRight) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    } else {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  })

  // Cookie获取函数
  const handleGetCookies = async () => {
    setLoading(true)
    setError('')
    setFastError('')
    setCookies('')

    try {
      const iwencaiUrl = 'https://www.iwencai.com/unifiedwap/result?w=%E6%B6%A8%E5%B9%85%E6%A6%9C&querytype=stock'
      const result = await window.electronAPI.getIwencaiCookies(iwencaiUrl)

      if (result.success) {
        setCookies(result.cookies || '')
        console.log('获取到的cookies:', result.cookies)
      } else {
        setError(result.error || '获取cookies失败')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setLoading(false)
    }
  }

  // 快速获取Cookie函数
  const handleGetCookiesFast = async () => {
    setFastLoading(true)
    setFastError('')
    setError('')
    setCookies('')
    setLastMethod('')
    setLastDuration(0)

    const startTime = Date.now()

    try {
      const result = await window.electronAPI.getIwencaiCookiesFast()
      const duration = Date.now() - startTime

      if (result.success) {
        setCookies(result.cookies || '')
        setLastMethod('快速获取（自动选择）')
        setLastDuration(duration)
        console.log('快速获取到的cookies:', result.cookies)
      } else {
        setFastError(result.error || '快速获取cookies失败')
      }
    } catch (err) {
      setFastError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setFastLoading(false)
    }
  }

  // API调用函数
  const handleCallAPI = async () => {
    if (!cookies) {
      setApiError('请先获取cookies')
      return
    }

    setApiLoading(true)
    setApiError('')
    setApiResult(null)

    try {
      const requestData: IwencaiRequestData = {
        source: "Ths_iwencai_Xuangu",
        version: "2.0",
        query_area: "",
        block_list: "",
        add_info: "{\"urp\":{\"scene\":1,\"company\":1,\"business\":1},\"contentType\":\"json\",\"searchInfo\":true}",
        question: question,
        perpage: "50",
        page: 1,
        secondary_intent: "stock",
        log_info: "{\"input_type\":\"click\"}",
        rsh: "Ths_iwencai_Xuangu_ly8c74fp3ynszbvjf2u2wrl6x6h11l3o"
      }

      const result = await window.electronAPI.callIwencaiAPI(cookies, requestData)

      if (result.success) {
        setApiResult(result.data)
        console.log('API调用成功:', result.data)
      } else {
        setApiError(result.error || 'API调用失败')
      }
    } catch (err) {
      setApiError(err instanceof Error ? err.message : '未知错误')
    } finally {
      setApiLoading(false)
    }
  }

  return (
    <div className="vscode-app" ref={containerRef}>
      {/* 顶部搜索栏 */}
      <div className="top-bar">
        <div className="search-container">
          <input
            type="text"
            className="search-input"
            placeholder="搜索股票代码或名称..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="main-content">
        {/* 左侧活动栏 */}
        <div className="activity-bar">
          <div className="activity-buttons">
            <button
              className={`activity-button ${activeView === 'home' ? 'active' : ''}`}
              onClick={() => setActiveView('home')}
              title="首页"
            >
              <span className="icon">🏠</span>
            </button>
            <button
              className={`activity-button ${activeView === 'watchlist' ? 'active' : ''}`}
              onClick={() => {
                setActiveView('watchlist')
                setWatchlistVisible(!watchlistVisible)
              }}
              title="自选股票"
            >
              <span className="icon">⭐</span>
            </button>
            <button
              className={`activity-button ${activeView === 'bidding' ? 'active' : ''}`}
              onClick={() => setActiveView('bidding')}
              title="竞价分析"
            >
              <span className="icon">📊</span>
            </button>
          </div>
        </div>

        {/* 左侧面板（自选列表） */}
        {watchlistVisible && (
          <>
            <div
              className="left-panel"
              style={{ width: `${leftPanelWidth}px` }}
            >
              <div className="panel-header">
                <h3>自选股票</h3>
                <button
                  className="close-button"
                  onClick={() => setWatchlistVisible(false)}
                >
                  ×
                </button>
              </div>
              <div className="panel-content">
                <div className="watchlist-item">
                  <div className="stock-code">000001</div>
                  <div className="stock-name">平安银行</div>
                  <div className="stock-price">12.34</div>
                  <div className="stock-change positive">+2.34%</div>
                </div>
                <div className="watchlist-item">
                  <div className="stock-code">000002</div>
                  <div className="stock-name">万科A</div>
                  <div className="stock-price">8.76</div>
                  <div className="stock-change negative">-1.23%</div>
                </div>
                <div className="watchlist-item">
                  <div className="stock-code">300001</div>
                  <div className="stock-name">特锐德</div>
                  <div className="stock-price">15.67</div>
                  <div className="stock-change positive">+0.89%</div>
                </div>
              </div>
            </div>

            {/* 左侧拖拽分隔条 */}
            <div
              className="resize-handle left-resize"
              onMouseDown={() => setIsDraggingLeft(true)}
            />
          </>
        )}

        {/* 右侧主要内容区域 */}
        <div className="right-panel" style={{
          width: watchlistVisible ? `calc(100% - ${leftPanelWidth + 60}px)` : 'calc(100% - 60px)'
        }}>
          <div className="content-area">
            {activeView === 'home' && (
              <div className="home-content">
                <h2>问财数据获取工具</h2>

                {/* Cookie获取区域 */}
                <div className="function-section">
                  <h3>Cookie获取</h3>
                  <div className="button-group">
                    <button
                      className="primary-button"
                      onClick={handleGetCookies}
                      disabled={loading || fastLoading}
                    >
                      {loading ? '获取中...' : '完整获取Cookie'}
                    </button>
                    <button
                      className="secondary-button"
                      onClick={handleGetCookiesFast}
                      disabled={loading || fastLoading}
                    >
                      {fastLoading ? '快速获取中...' : '快速获取Cookie'}
                    </button>
                  </div>

                  {(error || fastError) && (
                    <div className="error">
                      {error || fastError}
                    </div>
                  )}

                  {cookies && (
                    <div className="success">
                      ✅ Cookie获取成功 {lastMethod && `(${lastMethod} - ${lastDuration}ms)`}
                    </div>
                  )}
                </div>

                {/* API调用区域 */}
                <div className="function-section">
                  <h3>API调用</h3>
                  <div className="input-group">
                    <label>查询问题:</label>
                    <input
                      type="text"
                      value={question}
                      onChange={(e) => setQuestion(e.target.value)}
                      placeholder="输入查询问题，如：6月份个股涨幅排名创业板"
                      className="text-input"
                    />
                  </div>

                  <button
                    className="primary-button"
                    onClick={handleCallAPI}
                    disabled={apiLoading || !cookies}
                  >
                    {apiLoading ? '调用中...' : '调用问财API'}
                  </button>

                  {!cookies && (
                    <div className="warning">
                      请先获取Cookie
                    </div>
                  )}

                  {apiError && (
                    <div className="error">
                      {apiError}
                    </div>
                  )}

                  {apiResult && (
                    <div className="result-section">
                      <h4>API响应结果:</h4>
                      <textarea
                        value={JSON.stringify(apiResult, null, 2)}
                        readOnly
                        className="result-textarea"
                      />
                      <button
                        onClick={() => navigator.clipboard.writeText(JSON.stringify(apiResult, null, 2))}
                        className="copy-button"
                      >
                        复制结果
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeView === 'watchlist' && (
              <div className="watchlist-content">
                <h2>自选股票详情</h2>
                <p>这里将显示选中股票的详细信息</p>
              </div>
            )}

            {activeView === 'bidding' && (
              <div className="bidding-content">
                <h2>竞价分析</h2>
                <p>这里将显示竞价相关数据</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* 底部状态栏 */}
      <div className="status-bar">
        <div className="status-left">
          <span className="status-item">
            <span className="status-indicator ready"></span>
            {(loading || fastLoading || apiLoading) ? '处理中...' : '就绪'}
          </span>
          <span className="status-item">
            <span className="status-indicator connected"></span>
            连接状态: 已连接
          </span>
          {activeView === 'watchlist' && (
            <span className="status-item">
              📈 自选股票: 3只
            </span>
          )}
        </div>
        <div className="status-right">
          <span className="status-item">
            <span className={`status-indicator ${cookies ? 'success' : 'warning'}`}></span>
            Cookie: {cookies ? '已获取' : '未获取'}
          </span>
          <span className="status-item">
            <span className={`status-indicator ${apiLoading ? 'loading' : 'ready'}`}></span>
            API: {apiLoading ? '调用中' : '就绪'}
          </span>
          {lastMethod && lastDuration > 0 && (
            <span className="status-item">
              ⚡ {(lastDuration/1000).toFixed(1)}s
            </span>
          )}
        </div>
      </div>
    </div>
  )
}

export default App
