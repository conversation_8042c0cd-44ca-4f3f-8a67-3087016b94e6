/* VSCode风格的应用样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #1e1e1e;
  color: #cccccc;
  overflow: hidden;
}

.vscode-app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #1e1e1e;
}

/* 顶部搜索栏 */
.top-bar {
  height: 35px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  display: flex;
  align-items: center;
  padding: 0 10px;
}

.search-container {
  flex: 1;
  max-width: 600px;
  margin: 0 auto;
}

.search-input {
  width: 100%;
  height: 22px;
  background-color: #3c3c3c;
  border: 1px solid #464647;
  border-radius: 3px;
  color: #cccccc;
  padding: 0 8px;
  font-size: 13px;
  outline: none;
}

.search-input:focus {
  border-color: #007acc;
  background-color: #1e1e1e;
}

.search-input::placeholder {
  color: #6a6a6a;
}

/* 主要内容区域 */
.main-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧活动栏 */
.activity-bar {
  width: 48px;
  background-color: #2d2d30;
  border-right: 1px solid #3e3e42;
  display: flex;
  flex-direction: column;
}

.activity-buttons {
  display: flex;
  flex-direction: column;
  padding: 8px 0;
}

.activity-button {
  width: 48px;
  height: 48px;
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: background-color 0.2s;
}

.activity-button:hover {
  background-color: #37373d;
}

.activity-button.active {
  background-color: #37373d;
  color: #ffffff;
}

.activity-button.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #007acc;
}

.activity-button .icon {
  font-size: 16px;
}

/* 左侧面板 */
.left-panel {
  background-color: #252526;
  border-right: 1px solid #3e3e42;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  max-width: 500px;
}

.panel-header {
  height: 35px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
}

.panel-header h3 {
  font-size: 13px;
  font-weight: 600;
  color: #cccccc;
  text-transform: uppercase;
}

.close-button {
  background: none;
  border: none;
  color: #cccccc;
  cursor: pointer;
  font-size: 16px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
}

.close-button:hover {
  background-color: #37373d;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

/* 自选股票列表 */
.watchlist-item {
  display: grid;
  grid-template-columns: 80px 1fr 80px 80px;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 2px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-size: 13px;
}

.watchlist-item:hover {
  background-color: #2a2d2e;
}

.stock-code {
  color: #569cd6;
  font-weight: 600;
}

.stock-name {
  color: #cccccc;
}

.stock-price {
  color: #cccccc;
  text-align: right;
}

.stock-change {
  text-align: right;
  font-weight: 600;
}

.stock-change.positive {
  color: #4ec9b0;
}

.stock-change.negative {
  color: #f44747;
}

/* 拖拽分隔条 */
.resize-handle {
  width: 4px;
  background-color: transparent;
  cursor: col-resize;
  position: relative;
  transition: background-color 0.2s;
}

.resize-handle:hover {
  background-color: #007acc;
}

.resize-handle.left-resize {
  border-right: 1px solid #3e3e42;
}

.resize-handle.right-resize {
  border-left: 1px solid #3e3e42;
}

/* 右侧主要内容区域 */
.right-panel {
  flex: 1;
  background-color: #1e1e1e;
  display: flex;
  flex-direction: column;
}

.content-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.home-content,
.watchlist-content,
.bidding-content {
  max-width: 800px;
}

.home-content h2,
.watchlist-content h2,
.bidding-content h2 {
  color: #ffffff;
  font-size: 24px;
  margin-bottom: 16px;
  font-weight: 600;
}

.home-content p,
.watchlist-content p,
.bidding-content p {
  color: #cccccc;
  font-size: 14px;
  line-height: 1.5;
}

/* 底部状态栏 */
.status-bar {
  height: 22px;
  background-color: #007acc;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  font-size: 12px;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
  background: #424242;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #4f4f4f;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .activity-bar {
    width: 40px;
  }

  .activity-button {
    width: 40px;
    height: 40px;
  }

  .left-panel {
    min-width: 250px;
  }

  .search-container {
    max-width: 400px;
  }
}

/* 动画效果 */
.left-panel {
  transition: width 0.3s ease;
}

.right-panel {
  transition: width 0.3s ease;
}

/* 选中状态 */
.watchlist-item.selected {
  background-color: #094771;
  border-left: 3px solid #007acc;
}

/* 加载状态 */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* 错误状态 */
.error {
  color: #f44747;
  background-color: #5a1d1d;
  border: 1px solid #be1100;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px 0;
  font-size: 13px;
}

/* 成功状态 */
.success {
  color: #4ec9b0;
  background-color: #1e3a32;
  border: 1px solid #14ce9c;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px 0;
  font-size: 13px;
}

/* 警告状态 */
.warning {
  color: #ffcc02;
  background-color: #3d3a1e;
  border: 1px solid #ffcc02;
  border-radius: 4px;
  padding: 8px 12px;
  margin: 8px 0;
  font-size: 13px;
}

/* 功能区域 */
.function-section {
  background-color: #252526;
  border: 1px solid #3e3e42;
  border-radius: 6px;
  padding: 20px;
  margin-bottom: 20px;
}

.function-section h3 {
  color: #ffffff;
  font-size: 18px;
  margin-bottom: 16px;
  font-weight: 600;
  border-bottom: 1px solid #3e3e42;
  padding-bottom: 8px;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

/* 按钮样式 */
.primary-button {
  background-color: #0e639c;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s;
  font-weight: 500;
}

.primary-button:hover:not(:disabled) {
  background-color: #1177bb;
}

.primary-button:disabled {
  background-color: #3e3e42;
  color: #6a6a6a;
  cursor: not-allowed;
}

.secondary-button {
  background-color: #37373d;
  color: #cccccc;
  border: 1px solid #464647;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.secondary-button:hover:not(:disabled) {
  background-color: #464647;
  border-color: #6a6a6a;
}

.secondary-button:disabled {
  background-color: #2d2d30;
  color: #6a6a6a;
  cursor: not-allowed;
}

.copy-button {
  background-color: #28a745;
  color: #ffffff;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 8px;
  transition: background-color 0.2s;
}

.copy-button:hover {
  background-color: #34ce57;
}

/* 输入组 */
.input-group {
  margin-bottom: 16px;
}

.input-group label {
  display: block;
  color: #cccccc;
  font-size: 14px;
  margin-bottom: 6px;
  font-weight: 500;
}

.text-input {
  width: 100%;
  background-color: #3c3c3c;
  border: 1px solid #464647;
  border-radius: 4px;
  color: #cccccc;
  padding: 8px 12px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.text-input:focus {
  border-color: #007acc;
  background-color: #1e1e1e;
}

.text-input::placeholder {
  color: #6a6a6a;
}

/* 结果区域 */
.result-section {
  margin-top: 16px;
}

.result-section h4 {
  color: #ffffff;
  font-size: 16px;
  margin-bottom: 12px;
  font-weight: 600;
}

.result-textarea {
  width: 100%;
  height: 300px;
  background-color: #1e1e1e;
  border: 1px solid #464647;
  border-radius: 4px;
  color: #cccccc;
  padding: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  resize: vertical;
  outline: none;
}

.result-textarea:focus {
  border-color: #007acc;
}

/* 状态指示器 */
.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 6px;
}

.status-indicator.ready {
  background-color: #4ec9b0;
}

.status-indicator.connected {
  background-color: #4ec9b0;
}

.status-indicator.success {
  background-color: #4ec9b0;
}

.status-indicator.warning {
  background-color: #ffcc02;
}

.status-indicator.loading {
  background-color: #007acc;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* 改进的自选股票项 */
.watchlist-item {
  display: grid;
  grid-template-columns: 80px 1fr 80px 80px;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 2px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 13px;
  border-left: 3px solid transparent;
}

.watchlist-item:hover {
  background-color: #2a2d2e;
  border-left-color: #007acc;
}

.watchlist-item.selected {
  background-color: #094771;
  border-left-color: #007acc;
}

/* 改进的面板头部 */
.panel-header {
  height: 35px;
  background-color: #2d2d30;
  border-bottom: 1px solid #3e3e42;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  position: relative;
}

.panel-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, #007acc 0%, transparent 100%);
}

/* 改进的搜索输入框 */
.search-input {
  width: 100%;
  height: 22px;
  background-color: #3c3c3c;
  border: 1px solid #464647;
  border-radius: 3px;
  color: #cccccc;
  padding: 0 8px 0 24px;
  font-size: 13px;
  outline: none;
  position: relative;
}

.search-container {
  flex: 1;
  max-width: 600px;
  margin: 0 auto;
  position: relative;
}

.search-container::before {
  content: '🔍';
  position: absolute;
  left: 6px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 12px;
  color: #6a6a6a;
  z-index: 1;
}

.electron-info {
  margin-top: 2em;
  padding: 1em;
  background-color: #f0f0f0;
  border-radius: 8px;
  font-size: 0.9em;
}

.electron-info p {
  margin: 0;
}
