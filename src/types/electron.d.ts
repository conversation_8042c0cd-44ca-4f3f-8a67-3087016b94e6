// 问财API请求数据类型
export interface IwencaiRequestData {
  source: string;
  version: string;
  query_area: string;
  block_list: string;
  add_info: string;
  question: string;
  perpage: string;
  page: number;
  secondary_intent: string;
  log_info: string;
  rsh: string;
  hexin_v?: string;
}

// Electron API类型定义
export interface IElectronAPI {
  getIwencaiCookies: (url: string) => Promise<{
    success: boolean;
    cookies?: string;
    error?: string;
  }>;
  callIwencaiAPI: (cookies: string, requestData: IwencaiRequestData) => Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }>;
}

declare global {
  interface Window {
    electronAPI: IElectronAPI;
  }
}
