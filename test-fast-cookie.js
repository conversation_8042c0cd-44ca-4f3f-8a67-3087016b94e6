// 测试快速获取问财cookie功能
const { app, BrowserWindow } = require('electron');
const { net } = require('electron');

async function testFastCookieRetrieval() {
  await app.whenReady();
  
  console.log('开始测试快速cookie获取功能...');
  console.time('快速获取耗时');
  
  try {
    // 快速获取cookies的函数
    const getIwencaiCookiesFast = () => {
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('快速获取cookie超时'));
        }, 10000);

        const request = net.request({
          method: 'GET',
          url: 'https://www.iwencai.com/unifiedwap/result?w=test&querytype=stock',
          headers: {
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"'
          }
        });

        request.on('response', (response) => {
          const setCookieHeaders = response.headers['set-cookie'] || [];
          const cookies = [];
          
          setCookieHeaders.forEach((cookieHeader) => {
            const cookieParts = cookieHeader.split(';')[0];
            if (cookieParts && cookieParts.includes('=')) {
              cookies.push(cookieParts.trim());
            }
          });

          const cookieString = cookies.join('; ');
          console.log('快速获取到的cookies:', cookieString);
          console.log('获取到的cookies数量:', cookies.length);
          
          clearTimeout(timeout);
          resolve(cookieString);
          
          // 消费响应数据
          response.on('data', () => {});
          response.on('end', () => {});
        });

        request.on('error', (error) => {
          clearTimeout(timeout);
          reject(new Error(`快速获取cookie失败: ${error.message}`));
        });

        request.end();
      });
    };

    const cookies = await getIwencaiCookiesFast();
    console.timeEnd('快速获取耗时');
    
    if (cookies) {
      console.log('✅ 快速获取成功!');
      console.log('Cookie字符串长度:', cookies.length);
    } else {
      console.log('⚠️ 快速获取成功但没有获取到cookies');
    }
    
  } catch (error) {
    console.timeEnd('快速获取耗时');
    console.error('❌ 快速获取失败:', error.message);
  }
  
  app.quit();
}

testFastCookieRetrieval();
