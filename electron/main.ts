const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 保持对window对象的全局引用，如果不这样做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow = null as any;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用
  // 开发环境下，加载开发服务器地址
  mainWindow.loadURL('http://localhost:5173');
  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时，调用这个方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭后退出应用
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (mainWindow === null) {
    createWindow();
  }
});

// 获取问财网站cookie的函数
async function getIwencaiCookies(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    // 创建隐藏的浏览器窗口
    const hiddenWindow = new BrowserWindow({
      width: 800,
      height: 600,
      show: false, // 隐藏窗口
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    // 设置超时
    const timeout = setTimeout(() => {
      hiddenWindow.close();
      reject(new Error('获取cookie超时'));
    }, 30000); // 30秒超时

    // 页面加载完成后获取cookies
    hiddenWindow.webContents.once('did-finish-load', async () => {
      try {
        // 等待一段时间确保页面完全加载和JavaScript执行
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 获取所有cookies
        const cookies = await hiddenWindow.webContents.session.cookies.get({
          domain: '.iwencai.com'
        });

        // 将cookies转换为字符串格式
        const cookieString = cookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');

        clearTimeout(timeout);
        hiddenWindow.close();
        resolve(cookieString);
      } catch (error) {
        clearTimeout(timeout);
        hiddenWindow.close();
        reject(error);
      }
    });

    // 处理加载错误
    hiddenWindow.webContents.once('did-fail-load', (event: any, errorCode: any, errorDescription: any) => {
      clearTimeout(timeout);
      hiddenWindow.close();
      reject(new Error(`页面加载失败: ${errorDescription}`));
    });

    // 加载目标URL
    hiddenWindow.loadURL(url);
  });
}

// 调用问财API的函数
async function callIwencaiAPI(cookies: string, requestData: any): Promise<any> {
  const { net } = require('electron');

  return new Promise((resolve, reject) => {
    const request = net.request({
      method: 'POST',
      url: 'https://www.iwencai.com/customized/chart/get-robot-data',
      headers: {
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Content-Type': 'application/json',
        'Origin': 'https://www.iwencai.com',
        'Pragma': 'no-cache',
        'Referer': 'https://www.iwencai.com/unifiedwap/result?w=6%E6%9C%88%E4%BB%BD%E4%B8%AA%E8%82%A1%E6%B6%A8%E5%B9%85%E6%8E%92%E5%90%8D%E5%88%9B%E4%B8%9A%E6%9D%BF&querytype=stock',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'sec-ch-ua': '"Google Chrome";v="137", "Chromium";v="137", "Not/A)Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'Cookie': cookies
      }
    });

    let responseData = '';

    request.on('response', (response: any) => {
      response.on('data', (chunk: any) => {
        responseData += chunk.toString();
      });

      response.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error(`解析响应失败: ${error}`));
        }
      });
    });

    request.on('error', (error: any) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    // 发送请求数据
    request.write(JSON.stringify(requestData));
    request.end();
  });
}

// 快速获取问财网站cookie的函数（轻量级浏览器方法）
async function getIwencaiCookiesFast(): Promise<string> {
  return new Promise((resolve, reject) => {
    // 创建轻量级隐藏浏览器窗口
    const hiddenWindow = new BrowserWindow({
      width: 400,
      height: 300,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
        images: false, // 禁用图片加载
        javascript: true, // 保持JavaScript启用，因为可能需要执行JS来设置cookies
      }
    });

    // 设置较短的超时时间
    const timeout = setTimeout(() => {
      hiddenWindow.close();
      reject(new Error('快速获取cookie超时'));
    }, 8000); // 8秒超时

    // 页面开始加载时就尝试获取cookies（不等待完全加载）
    hiddenWindow.webContents.once('did-start-loading', () => {
      setTimeout(async () => {
        try {
          // 获取当前已设置的cookies
          const cookies = await hiddenWindow.webContents.session.cookies.get({
            domain: '.iwencai.com'
          });

          if (cookies.length > 0) {
            const cookieString = cookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');
            console.log('快速获取到的cookies:', cookieString);
            clearTimeout(timeout);
            hiddenWindow.close();
            resolve(cookieString);
            return;
          }
        } catch (error) {
          console.log('快速获取过程中出错，继续等待...');
        }
      }, 2000); // 2秒后检查
    });

    // 页面加载完成后再次尝试
    hiddenWindow.webContents.once('did-finish-load', async () => {
      try {
        // 短暂等待JavaScript执行
        await new Promise(resolve => setTimeout(resolve, 1000));

        const cookies = await hiddenWindow.webContents.session.cookies.get({
          domain: '.iwencai.com'
        });

        const cookieString = cookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');
        console.log('快速获取到的cookies:', cookieString);

        clearTimeout(timeout);
        hiddenWindow.close();
        resolve(cookieString);
      } catch (error) {
        clearTimeout(timeout);
        hiddenWindow.close();
        reject(error);
      }
    });

    // 处理加载错误
    hiddenWindow.webContents.once('did-fail-load', (event: any, errorCode: any, errorDescription: any) => {
      clearTimeout(timeout);
      hiddenWindow.close();
      reject(new Error(`快速加载失败: ${errorDescription}`));
    });

    // 加载问财首页（通常比具体查询页面更快）
    hiddenWindow.loadURL('https://www.iwencai.com/');
  });
}

// 从API端点获取cookies的备用方法
async function getIwencaiCookiesFromAPI(): Promise<string> {
  const { net } = require('electron');

  return new Promise((resolve, reject) => {
    const timeout = setTimeout(() => {
      reject(new Error('从API端点获取cookie超时'));
    }, 8000); // 8秒超时

    const request = net.request({
      method: 'GET',
      url: 'https://www.iwencai.com/unifiedwap/result?w=test&querytype=stock',
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
      }
    });

    request.on('response', (response: any) => {
      const setCookieHeaders = response.headers['set-cookie'] || [];
      const cookies: string[] = [];

      setCookieHeaders.forEach((cookieHeader: string) => {
        const cookieParts = cookieHeader.split(';')[0];
        if (cookieParts && cookieParts.includes('=')) {
          cookies.push(cookieParts.trim());
        }
      });

      const cookieString = cookies.join('; ');
      console.log('从API端点获取到的cookies:', cookieString);

      clearTimeout(timeout);
      resolve(cookieString);

      // 消费响应数据
      response.on('data', () => {});
      response.on('end', () => {});
    });

    request.on('error', (error: any) => {
      clearTimeout(timeout);
      reject(new Error(`从API端点获取cookie失败: ${error.message}`));
    });

    request.end();
  });
}

// IPC处理器：获取问财cookie（原方法）
ipcMain.handle('get-iwencai-cookies', async (event: any, url: string) => {
  try {
    const cookies = await getIwencaiCookies(url);
    return { success: true, cookies };
  } catch (error) {
    console.error('获取cookie失败:', error);
    return { success: false, error: error instanceof Error ? error.message : '未知错误' };
  }
});

// Session复用方法获取cookies
async function getIwencaiCookiesWithSession(): Promise<string> {
  const { session } = require('electron');

  return new Promise((resolve, reject) => {
    // 使用持久化session
    const persistentSession = session.fromPartition('persist:iwencai');

    // 创建轻量级窗口
    const hiddenWindow = new BrowserWindow({
      width: 300,
      height: 200,
      show: false,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
        session: persistentSession,
        images: false,
        javascript: true
      }
    });

    const timeout = setTimeout(() => {
      hiddenWindow.close();
      reject(new Error('Session方法获取cookie超时'));
    }, 6000); // 6秒超时

    // 先检查session中是否已有cookies
    persistentSession.cookies.get({ domain: '.iwencai.com' }).then((existingCookies: any) => {
      if (existingCookies.length > 0) {
        const cookieString = existingCookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');
        console.log('从session获取到的cookies:', cookieString);
        clearTimeout(timeout);
        hiddenWindow.close();
        resolve(cookieString);
        return;
      }

      // 如果没有现有cookies，快速加载页面获取
      hiddenWindow.webContents.once('did-finish-load', async () => {
        try {
          await new Promise(resolve => setTimeout(resolve, 500)); // 只等待0.5秒

          const cookies = await persistentSession.cookies.get({ domain: '.iwencai.com' });
          const cookieString = cookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');

          clearTimeout(timeout);
          hiddenWindow.close();
          resolve(cookieString);
        } catch (error) {
          clearTimeout(timeout);
          hiddenWindow.close();
          reject(error);
        }
      });

      hiddenWindow.loadURL('https://www.iwencai.com/');
    }).catch((error: any) => {
      clearTimeout(timeout);
      hiddenWindow.close();
      reject(error);
    });
  });
}

// 预设cookie方法（备用方案）
async function getIwencaiCookiesPreset(): Promise<string> {
  // 生成一些基本的cookie值
  const timestamp = Date.now();
  const randomId = Math.random().toString(36).substring(2, 15);

  // 基于观察到的cookie模式生成
  const presetCookies = [
    `other_uid=Ths_iwencai_Xuangu_${randomId}`,
    `ta_random_userid=${randomId.substring(0, 10)}`,
    `cid=${Math.random().toString(36).substring(2, 15)}${timestamp}`,
    `v=${Math.random().toString(36).substring(2, 15).toUpperCase()}`
  ];

  const cookieString = presetCookies.join('; ');
  console.log('生成的预设cookies:', cookieString);

  return cookieString;
}

// IPC处理器：快速获取问财cookie
ipcMain.handle('get-iwencai-cookies-fast', async (event: any) => {
  try {
    console.log('尝试快速方法1: 轻量级浏览器');
    let cookies = await getIwencaiCookiesFast();

    if (!cookies || cookies.length < 10) {
      console.log('尝试快速方法2: Session复用');
      cookies = await getIwencaiCookiesWithSession();
    }

    if (!cookies || cookies.length < 10) {
      console.log('尝试快速方法3: 预设cookie');
      cookies = await getIwencaiCookiesPreset();
    }

    return { success: true, cookies };
  } catch (error) {
    console.error('所有快速获取方法都失败，尝试预设方案:', error);
    try {
      const cookies = await getIwencaiCookiesPreset();
      return { success: true, cookies };
    } catch (presetError) {
      return { success: false, error: error instanceof Error ? error.message : '未知错误' };
    }
  }
});

// IPC处理器：调用问财API
ipcMain.handle('call-iwencai-api', async (event: any, cookies: string, requestData: any) => {
  try {
    const result = await callIwencaiAPI(cookies, requestData);
    return { success: true, data: result };
  } catch (error) {
    console.error('调用API失败:', error);
    return { success: false, error: error instanceof Error ? error.message : '未知错误' };
  }
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。
