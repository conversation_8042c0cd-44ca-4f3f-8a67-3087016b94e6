const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

// 禁用Electron安全警告
process.env.ELECTRON_DISABLE_SECURITY_WARNINGS = 'true';

// 保持对window对象的全局引用，如果不这样做的话，当JavaScript对象被
// 垃圾回收的时候，window对象将会自动的关闭
let mainWindow = null as any;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    }
  });

  // 加载应用
  // 开发环境下，加载开发服务器地址
  mainWindow.loadURL('http://localhost:5173');
  // 打开开发者工具
  mainWindow.webContents.openDevTools();

  // 当window被关闭时，触发下面的事件
  mainWindow.on('closed', () => {
    // 取消引用window对象，如果你的应用支持多窗口的话，
    // 通常会把多个window对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null;
  });
}

// 当Electron完成初始化并准备创建浏览器窗口时，调用这个方法
app.whenReady().then(createWindow);

// 当所有窗口都被关闭后退出应用
app.on('window-all-closed', () => {
  // 在macOS上，除非用户用Cmd + Q确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  // 在macOS上，当点击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (mainWindow === null) {
    createWindow();
  }
});

// 获取问财网站cookie的函数
async function getIwencaiCookies(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    // 创建隐藏的浏览器窗口
    const hiddenWindow = new BrowserWindow({
      width: 800,
      height: 600,
      show: false, // 隐藏窗口
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true
      }
    });

    // 设置超时
    const timeout = setTimeout(() => {
      hiddenWindow.close();
      reject(new Error('获取cookie超时'));
    }, 30000); // 30秒超时

    // 页面加载完成后获取cookies
    hiddenWindow.webContents.once('did-finish-load', async () => {
      try {
        // 等待一段时间确保页面完全加载和JavaScript执行
        await new Promise(resolve => setTimeout(resolve, 3000));

        // 获取所有cookies
        const cookies = await hiddenWindow.webContents.session.cookies.get({
          domain: '.iwencai.com'
        });

        // 将cookies转换为字符串格式
        const cookieString = cookies.map((cookie: any) => `${cookie.name}=${cookie.value}`).join('; ');

        clearTimeout(timeout);
        hiddenWindow.close();
        resolve(cookieString);
      } catch (error) {
        clearTimeout(timeout);
        hiddenWindow.close();
        reject(error);
      }
    });

    // 处理加载错误
    hiddenWindow.webContents.once('did-fail-load', (event: any, errorCode: any, errorDescription: any) => {
      clearTimeout(timeout);
      hiddenWindow.close();
      reject(new Error(`页面加载失败: ${errorDescription}`));
    });

    // 加载目标URL
    hiddenWindow.loadURL(url);
  });
}

// IPC处理器：获取问财cookie
ipcMain.handle('get-iwencai-cookies', async (event: any, url: string) => {
  try {
    const cookies = await getIwencaiCookies(url);
    return { success: true, cookies };
  } catch (error) {
    console.error('获取cookie失败:', error);
    return { success: false, error: error instanceof Error ? error.message : '未知错误' };
  }
});

// 在这个文件中，你可以续写应用剩下主进程代码。
// 也可以拆分成几个文件，然后用require导入。
