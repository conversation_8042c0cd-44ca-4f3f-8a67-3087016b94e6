const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 所有的Node.js API都可以在预加载过程中使用。
// 它拥有与Chrome扩展一样的沙盒。
window.addEventListener('DOMContentLoaded', () => {
  const replaceText = (selector: string, text: string) => {
    const element = document.getElementById(selector);
    if (element) {
      element.innerText = text;
    }
  };

  for (const dependency of ['chrome', 'node', 'electron']) {
    replaceText(`${dependency}-version`, process.versions[dependency] as string);
  }
});

// 暴露API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 获取问财网站cookies
  getIwencaiCookies: (url: string) => ipcRenderer.invoke('get-iwencai-cookies', url)
});

// 使用CommonJS导出
module.exports = {};
